package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    List<FeeDto.FeeDueReportResponse> reportData = generateFeeDueReport(orgSlug, request);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    return students.stream()
        .map(
            student -> {
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, feeHeadsByStudent.get(student));
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";
    List<FeeHead> feeHeads = switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };

    if (request.feeGroupTypes() != null && !request.feeGroupTypes().isEmpty()) {
      return feeHeads.stream()
          .filter(feeHead -> {
            String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getDescription();
            return request.feeGroupTypes().stream()
                .anyMatch(feeGroupType -> feeGroupDescription != null &&
                    feeGroupDescription.toLowerCase().contains(feeGroupType.toLowerCase()));
          })
          .collect(Collectors.toList());
    }

    return feeHeads;
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeMaster().getFeeGroup().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<List<String>> csvBody = buildCsvBody(reportData);
    List<String> csvHeaders = csvBody.isEmpty() ? List.of() : csvBody.remove(0);

    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvBody, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(List<FeeDto.FeeDueReportResponse> reportData) {
    List<List<String>> csvBody = new ArrayList<>();

    Map<String, Set<String>> feeGroupPeriods = new LinkedHashMap<>();

    for (FeeDto.FeeDueReportResponse report : reportData) {
      for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
        String feeGroup = detail.feeTypeName();
        String period = determinePeriod(detail, feeGroup);
        feeGroupPeriods.computeIfAbsent(feeGroup, k -> new LinkedHashSet<>()).add(period);
      }
    }

    List<String> headers = new ArrayList<>();
    headers.addAll(
        List.of("Student Name", "Admission Number", "Roll Number", "Section", "Date of Admission"));

    List<String> dynamicHeaderKeys = new ArrayList<>();

    for (Map.Entry<String, Set<String>> entry : feeGroupPeriods.entrySet()) {
      String feeGroup = entry.getKey();
      Set<String> periods = entry.getValue();

      if (periods.size() > 1) {
        for (String period : periods) {
          String headerKey = period;
          headers.add(headerKey);
          dynamicHeaderKeys.add(feeGroup + " - " + period);
        }
      } else {
        headers.add(feeGroup);
        dynamicHeaderKeys.add(feeGroup + " - " + periods.iterator().next());
      }

      headers.add("Total Due");
      dynamicHeaderKeys.add(feeGroup + " - TOTAL");
    }

    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      Map<String, Double> valuesByHeader = new HashMap<>();
      Map<String, Double> feeGroupTotals = new HashMap<>();

      for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
        String feeGroup = detail.feeTypeName();
        String period = determinePeriod(detail, feeGroup);
        String key = feeGroup + " - " + period;

        double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;
        valuesByHeader.merge(key, amount, Double::sum);
        feeGroupTotals.merge(feeGroup, amount, Double::sum);
      }

      List<String> row = new ArrayList<>();
      row.add(report.studentName());
      row.add(report.admissionNumber());
      row.add(report.rollNumber());
      row.add(report.sectionName());
      row.add(report.dateOfAdmission());

      for (String headerKey : dynamicHeaderKeys) {
        row.add(String.format("%.0f", valuesByHeader.getOrDefault(headerKey, 0.0)));
      }

      row.add(String.format("%.0f", report.discountAmount()));
      row.add(String.format("%.0f", report.totalDueAmount()));
      csvBody.add(row);
    }

    return csvBody;
  }

  private String determinePeriod(FeeDto.FeeDetailResponse detail, String feeGroup) {
    if (feeGroup != null && (feeGroup.toLowerCase().contains("tuition") ||
                            feeGroup.toLowerCase().contains("term"))) {
      if (detail.month() != null && !detail.month().isEmpty()) {
        String month = detail.month().toUpperCase();
        return switch (month) {
          case "APR", "MAY", "JUN" -> "Term 1";
          case "JUL", "AUG", "SEP" -> "Term 2";
          case "OCT", "NOV", "DEC" -> "Term 3";
          case "JAN", "FEB", "MAR" -> "Term 4";
          default -> "Term 1";
        };
      }
      return "Term 1";
    } else {
      return (detail.month() != null && !detail.month().isEmpty()) ? detail.month() : "TOTAL";
    }
  }
}
